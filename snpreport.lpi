<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <PathDelim Value="\"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="SNP Report Generator"/>
      <UseAppBundle Value="False"/>
      <ResourceType Value="res"/>
    </General>
    <VersionInfo>
      <UseVersionInfo Value="True"/>
      <AutoIncrementBuild Value="True"/>
      <MajorVersionNr Value="1"/>
      <BuildNr Value="4"/>
      <StringTable CompanyName="Ritchie Low (2025)" FileDescription="SnP Report Generator" ProductName="SnP report generator" ProductVersion="*******"/>
    </VersionInfo>
    <BuildModes>
      <Item Name="Default" Default="True"/>
      <Item Name="Debug">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="snpreport"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value=".;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase\;D:\lazarus4\components\lazutils\lib\x86_64-win64\;D:\lazarus4\lcl\units\x86_64-win64\;C:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1\"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <Parsing>
            <SyntaxOptions>
              <SyntaxMode Value="Delphi"/>
              <CStyleOperator Value="False"/>
              <IncludeAssertionCode Value="True"/>
              <AllowLabel Value="False"/>
              <CPPInline Value="False"/>
            </SyntaxOptions>
          </Parsing>
          <CodeGeneration>
            <Checks>
              <IOChecks Value="True"/>
              <RangeChecks Value="True"/>
              <OverflowChecks Value="True"/>
              <StackChecks Value="True"/>
            </Checks>
            <VerifyObjMethodCallValidity Value="True"/>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <DebugInfoType Value="dsDwarf3"/>
              <UseHeaptrc Value="True"/>
              <TrashVariables Value="True"/>
              <UseExternalDbgSyms Value="True"/>
            </Debugging>
            <Options>
              <PassLinkerOptions Value="True"/>
              <LinkerOptions Value="-g"/>
            </Options>
          </Linking>
          <Other>
            <Verbosity>
              <ShowLineNum Value="True"/>
              <ShowAll Value="True"/>
            </Verbosity>
          </Other>
        </CompilerOptions>
      </Item>
      <Item Name="Release">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="snpreport"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value=".;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase\;D:\lazarus4\components\lazutils\lib\x86_64-win64\;D:\lazarus4\lcl\units\x86_64-win64\;C:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1\"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <Parsing>
            <SyntaxOptions>
              <SyntaxMode Value="Delphi"/>
              <CStyleOperator Value="False"/>
              <AllowLabel Value="False"/>
              <CPPInline Value="False"/>
            </SyntaxOptions>
          </Parsing>
          <CodeGeneration>
            <SmartLinkUnit Value="True"/>
            <Optimizations>
              <OptimizationLevel Value="3"/>
            </Optimizations>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <GenerateDebugInfo Value="False"/>
              <RunWithoutDebug Value="True"/>
              <StripSymbols Value="True"/>
            </Debugging>
            <LinkSmart Value="True"/>
            <Options>
              <LinkerOptions Value="-g"/>
            </Options>
          </Linking>
          <Other>
            <Verbosity>
              <ShowLineNum Value="True"/>
              <ShowAll Value="True"/>
            </Verbosity>
          </Other>
        </CompilerOptions>
      </Item>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
      <UseFileFilters Value="True"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <RequiredPackages>
      <Item>
        <PackageName Value="laz_synapse"/>
      </Item>
      <Item>
        <PackageName Value="FCL"/>
      </Item>
      <Item>
        <PackageName Value="LCLBase"/>
      </Item>
      <Item>
        <PackageName Value="LCL"/>
      </Item>
    </RequiredPackages>
    <Units>
      <Unit>
        <Filename Value="snpreport.lpr"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uMain.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uDB.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uReports.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uUtils.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="snpreport"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="$(ProjOutDir)"/>
      <OtherUnitFiles Value=".;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase\;D:\lazarus4\components\lazutils\lib\x86_64-win64\;D:\lazarus4\lcl\units\x86_64-win64\;C:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1\"/>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Parsing>
      <SyntaxOptions>
        <SyntaxMode Value="Delphi"/>
        <CStyleOperator Value="False"/>
        <IncludeAssertionCode Value="True"/>
        <AllowLabel Value="False"/>
        <CPPInline Value="False"/>
      </SyntaxOptions>
    </Parsing>
    <CodeGeneration>
      <SmartLinkUnit Value="True"/>
    </CodeGeneration>
    <Linking>
      <Debugging>
        <DebugInfoType Value="dsDwarf3"/>
        <UseHeaptrc Value="True"/>
        <TrashVariables Value="True"/>
        <UseExternalDbgSyms Value="True"/>
      </Debugging>
      <Options>
        <PassLinkerOptions Value="True"/>
        <LinkerOptions Value="-g"/>
      </Options>
    </Linking>
    <Other>
      <Verbosity>
        <ShowLineNum Value="True"/>
        <ShowAll Value="True"/>
      </Verbosity>
    </Other>
  </CompilerOptions>
</CONFIG>

program TestDirectories;

{$mode objfpc}{$H+}

uses
  Classes, SysUtils, Windows, ShlObj;

function ForceDirectories(const Dir: string): Boolean;
var
  Path: string;
begin
  Result := True;
  if Dir = '' then Exit;

  Path := ExcludeTrailingPathDelimiter(Dir);
  if (Length(Path) < 3) or DirectoryExists(Path) or (ExtractFilePath(Path) = Path) then
    Exit; // avoid 'xyz:\' problem.

  Result := ForceDirectories(ExtractFilePath(Path)) and CreateDir(Path);
end;

function GetProgramDataPath: string;
var
  Path: array[0..MAX_PATH] of Char;
begin
  if SHGetFolderPath(0, CSIDL_COMMON_APPDATA, 0, 0, @Path) = S_OK then
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\Reports'
  else
    Result := 'C:\ProgramData\SNP\Reports';
end;

function TestDirectoryWritable(const DirPath: string): Boolean;
var
  TestFile: TextFile;
  TestFilePath: string;
begin
  Result := False;
  if not DirectoryExists(DirPath) then
    Exit;

  TestFilePath := IncludeTrailingPathDelimiter(DirPath) + 'write_test_' + IntToStr(GetTickCount64) + '.tmp';

  try
    AssignFile(TestFile, TestFilePath);
    try
      Rewrite(TestFile);
      WriteLn(TestFile, 'Write test');
      CloseFile(TestFile);

      // If we got here, write was successful
      Result := True;

      // Clean up test file
      if FileExists(TestFilePath) then
        DeleteFile(PChar(TestFilePath));

    except
      // File operations failed - directory not writable
      Result := False;
      try
        CloseFile(TestFile);
      except
        // Ignore close errors
      end;
    end;
  except
    // Any other error means not writable
    Result := False;
  end;
end;

var
  ReportsDir: string;
begin
  WriteLn('Testing ProgramData directory access for SNP Reports...');
  WriteLn('');

  // Get the reports directory path
  ReportsDir := GetProgramDataPath;
  WriteLn('Reports directory: ', ReportsDir);

  // Check if directory exists
  if DirectoryExists(ReportsDir) then
    WriteLn('Directory exists: YES')
  else
  begin
    WriteLn('Directory exists: NO');
    WriteLn('Attempting to create directory...');
    try
      if ForceDirectories(ReportsDir) then
        WriteLn('Directory created successfully')
      else
        WriteLn('Failed to create directory');
    except
      on E: Exception do
        WriteLn('Error creating directory: ', E.Message);
    end;
  end;

  // Test write permissions
  if DirectoryExists(ReportsDir) then
  begin
    WriteLn('Testing write permissions...');
    if TestDirectoryWritable(ReportsDir) then
      WriteLn('Directory is WRITABLE')
    else
      WriteLn('Directory is NOT WRITABLE');
  end;

  WriteLn('');
  WriteLn('Test completed. Press Enter to continue...');
  ReadLn;
end.

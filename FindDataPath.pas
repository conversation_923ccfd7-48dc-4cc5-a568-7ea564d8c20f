procedure TYourDaemonService.ServiceExecute(Sender: TService);
var
  UserPaths: TStringList;
  FDBFiles: TStringList;
  Database: TIBDatabase;
  i, j: Integer;
begin
  while not Terminated do
  begin
    UserPaths := GetAllUserLocalAppDataPaths;
    try
      for i := 0 to UserPaths.Count - 1 do
      begin
        FDBFiles := FindFDBFiles(UserPaths[i]);
        try
          for j := 0 to FDBFiles.Count - 1 do
          begin
            // Process each user's FDB file
            if ProcessUserDatabase(FDBFiles[j]) then
            begin
              // Log successful processing
            end;
          end;
        finally
          FDBFiles.Free;
        end;
      end;
    finally
      UserPaths.Free;
    end;
    
    // Wait before next scan
    ServiceThread.ProcessRequests(False);
    Sleep(60000); // Check every minute
  end;
end;

// During installation, register each user's data path
procedure RegisterUserDataPath(const UserName, LocalAppDataPath: string);
var
  Reg: TRegistry;
begin
  Reg := TRegistry.Create(KEY_WRITE);
  try
    Reg.RootKey := HKEY_LOCAL_MACHINE;
    if Reg.OpenKey('SOFTWARE\YourCompany\YourService\Users\' + UserName, True) then
    begin
      Reg.WriteString('LocalAppDataPath', LocalAppDataPath);
      Reg.WriteString('DatabasePath', LocalAppDataPath + 'YourAppName\');
      Reg.CloseKey;
    end;
  finally
    Reg.Free;
  end;
end;

uses
  IBDatabase, IBQuery;

function ConnectToUserDatabase(const UserLocalAppPath, DatabaseName: string): TIBDatabase;
var
  DatabasePath: string;
  Database: TIBDatabase;
begin
  Result := nil;
  DatabasePath := IncludeTrailingPathDelimiter(UserLocalAppPath) + 
                  'YourAppName\' + DatabaseName;
  
  if FileExists(DatabasePath) then
  begin
    Database := TIBDatabase.Create(nil);
    try
      Database.DatabaseName := DatabasePath;
      Database.Params.Add('user_name=SYSDBA');
      Database.Params.Add('password=masterkey'); // Use your actual credentials
      Database.Params.Add('charset=UTF8');
      
      Database.Connected := True;
      Result := Database;
    except
      on E: Exception do
      begin
        Database.Free;
        // Log error: 'Failed to connect to ' + DatabasePath + ': ' + E.Message
      end;
    end;
  end;
end;

procedure ProcessAllUserDatabases;
var
  UserPaths: TStringList;
  FDBFiles: TStringList;
  Database: TIBDatabase;
  i, j: Integer;
begin
  UserPaths := GetAllUserLocalAppDataPaths;
  try
    for i := 0 to UserPaths.Count - 1 do
    begin
      FDBFiles := FindFDBFiles(UserPaths[i]);
      try
        for j := 0 to FDBFiles.Count - 1 do
        begin
          Database := ConnectToUserDatabase(UserPaths[i], ExtractFileName(FDBFiles[j]));
          if Assigned(Database) then
          try
            // Process your database operations here
            ProcessDatabase(Database);
          finally
            Database.Free;
          end;
        end;
      finally
        FDBFiles.Free;
      end;
    end;
  finally
    UserPaths.Free;
  end;
end;


---------------------

uses
  Windows, SysUtils, Classes;

function GetAllUserLocalAppDataPaths: TStringList;
var
  UsersDir: string;
  SearchRec: TSearchRec;
  UserPath: string;
begin
  Result := TStringList.Create;
  UsersDir := GetEnvironmentVariable('SYSTEMDRIVE') + '\Users\';
  
  if FindFirst(UsersDir + '*', faDirectory, SearchRec) = 0 then
  begin
    repeat
      if (SearchRec.Attr and faDirectory <> 0) and 
         (SearchRec.Name <> '.') and 
         (SearchRec.Name <> '..') and
         (SearchRec.Name <> 'Public') and
         (SearchRec.Name <> 'Default') then
      begin
        UserPath := UsersDir + SearchRec.Name + '\AppData\Local\';
        if DirectoryExists(UserPath) then
          Result.Add(UserPath);
      end;
    until FindNext(SearchRec) <> 0;
    FindClose(SearchRec);
  end;
end;

function FindFDBFiles(const BasePath: string): TStringList;
var
  SearchRec: TSearchRec;
  SearchPath: string;
begin
  Result := TStringList.Create;
  SearchPath := IncludeTrailingPathDelimiter(BasePath) + 'YourAppName\*.fdb';
  
  if FindFirst(SearchPath, faAnyFile and not faDirectory, SearchRec) = 0 then
  begin
    repeat
      Result.Add(BasePath + 'YourAppName\' + SearchRec.Name);
    until FindNext(SearchRec) <> 0;
    FindClose(SearchRec);
  end;
end;

function GetAllUserFDBFiles: TStringList;
var
  UserPaths: TStringList;
  FDBFiles: TStringList;
  i: Integer;
begin
  Result := TStringList.Create;
  UserPaths := GetAllUserLocalAppDataPaths;
  try
    for i := 0 to UserPaths.Count - 1 do
    begin
      FDBFiles := FindFDBFiles(UserPaths[i]);
      try
        Result.AddStrings(FDBFiles);
      finally
        FDBFiles.Free;
      end;
    end;
  finally
    UserPaths.Free;
  end;
end;


-----------------------

uses
  Registry, Classes, SysUtils;

type
  TUserDataConfig = record
    UserName: string;
    UserSID: string;
    LocalAppDataPath: string;
    DatabasePath: string;
  end;

function GetConfiguredUserPaths: TArray<TUserDataConfig>;
var
  Reg: TRegistry;
  UserList: TStringList;
  i: Integer;
  UserConfig: TUserDataConfig;
begin
  SetLength(Result, 0);
  Reg := TRegistry.Create(KEY_READ);
  UserList := TStringList.Create;
  try
    Reg.RootKey := HKEY_LOCAL_MACHINE;
    if Reg.OpenKey('SOFTWARE\YourCompany\YourService\Users', False) then
    begin
      Reg.GetKeyNames(UserList);
      SetLength(Result, UserList.Count);
      
      for i := 0 to UserList.Count - 1 do
      begin
        if Reg.OpenKey('SOFTWARE\YourCompany\YourService\Users\' + UserList[i], False) then
        begin
          UserConfig.UserName := UserList[i];
          UserConfig.LocalAppDataPath := Reg.ReadString('LocalAppDataPath');
          UserConfig.DatabasePath := Reg.ReadString('DatabasePath');
          Result[i] := UserConfig;
          Reg.CloseKey;
        end;
      end;
    end;
  finally
    UserList.Free;
    Reg.Free;
  end;
end;

------------------------------


<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <PathDelim Value="\"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="snp Daemon"/>
      <UseAppBundle Value="False"/>
      <ResourceType Value="res"/>
      <Icon Value="0"/>
    </General>
    <BuildModes>
      <Item Name="Debug" Default="True"/>
      <Item Name="Release">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="snpdaemon"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value=".;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process\;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase\;D:\lazarus4\components\lazutils\lib\x86_64-win64\;D:\lazarus4\lcl\units\x86_64-win64\;C:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1\"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <CodeGeneration>
            <SmartLinkUnit Value="True"/>
            <Optimizations>
              <OptimizationLevel Value="3"/>
            </Optimizations>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <GenerateDebugInfo Value="False"/>
              <DebugInfoType Value="dsDwarf3"/>
            </Debugging>
            <LinkSmart Value="True"/>
          </Linking>
        </CompilerOptions>
      </Item>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
      <UseFileFilters Value="True"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <RequiredPackages>
      <Item>
        <PackageName Value="laz_synapse"/>
      </Item>
      <Item>
        <PackageName Value="LazDaemon"/>
      </Item>
      <Item>
        <PackageName Value="LCL"/>
      </Item>
      <Item>
        <PackageName Value="FCL"/>
      </Item>
    </RequiredPackages>
    <Units>
      <Unit>
        <Filename Value="snpdaemon.lpr"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="daemonmapperunit.pas"/>
        <IsPartOfProject Value="True"/>
        <ComponentName Value="TestDaemonMapper"/>
        <HasResources Value="True"/>
        <ResourceBaseClass Value="Other"/>
        <ResourceBaseClassname Value="TDaemonMapper"/>
        <UnitName Value="DaemonMapperUnit"/>
      </Unit>
      <Unit>
        <Filename Value="daemonunit.pas"/>
        <IsPartOfProject Value="True"/>
        <ComponentName Value="snpDaemon"/>
        <HasResources Value="True"/>
        <ResourceBaseClass Value="DataModule"/>
        <UnitName Value="DaemonUnit"/>
      </Unit>
      <Unit>
        <Filename Value="daemonworkerthread.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="DaemonWorkerThread"/>
      </Unit>
      <Unit>
        <Filename Value="daemonsystemdinstallerunit.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="DaemonSystemdInstallerUnit"/>
      </Unit>
      <Unit>
        <Filename Value="uWorker.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="$(TargetCPU)-$(TargetOS)\Release\snpdaemon"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="$(ProjOutDir)"/>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Parsing>
      <SyntaxOptions>
        <IncludeAssertionCode Value="True"/>
      </SyntaxOptions>
    </Parsing>
    <CodeGeneration>
      <Checks>
        <IOChecks Value="True"/>
        <RangeChecks Value="True"/>
        <OverflowChecks Value="True"/>
        <StackChecks Value="True"/>
      </Checks>
      <VerifyObjMethodCallValidity Value="True"/>
    </CodeGeneration>
    <Linking>
      <Debugging>
        <DebugInfoType Value="dsDwarf3"/>
        <UseHeaptrc Value="True"/>
        <TrashVariables Value="True"/>
        <UseExternalDbgSyms Value="True"/>
      </Debugging>
    </Linking>
  </CompilerOptions>
  <Debugging>
    <Exceptions>
      <Item>
        <Name Value="EAbort"/>
      </Item>
      <Item>
        <Name Value="ECodetoolError"/>
      </Item>
      <Item>
        <Name Value="EFOpenError"/>
      </Item>
    </Exceptions>
  </Debugging>
</CONFIG>

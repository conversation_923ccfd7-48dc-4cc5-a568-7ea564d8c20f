program ConsoleWorker;

{$mode objfpc}{$H+}

uses
  <PERSON><PERSON>, SysUtils, uWorker, DateUtils;

var
  Worker: TReportWorker;
  Year, Month: Integer;
  Input: string;
  CurrentDate: TDateTime;
  PrevMonth, PrevYear, PrevDay: Word;
begin
  try
    // Create the worker
    // Enable debug mode for the console application
  Worker := TReportWorker.Create(False); // debug 
    try
      // // Get year input
      // Write('Enter year (or press Enter for current year): ');
      // ReadLn(Input);
      // if Input = '' then
      //   Year := YearOf(Now)
      // else
      //   Year := StrToInt(Input);

      // // Get month input
      // Write('Enter month (1-12, or press Enter for current month): ');
      // ReadLn(Input);
      // if Input = '' then
      //   Month := MonthOf(Now)
      // else
      //   Month := StrToInt(Input);
      CurrentDate := Now;
      DecodeDate(IncMonth(CurrentDate, -1), PrevYear, PrevMonth, PrevDay);

      // Generate the report
      // WriteLn('Generating report for ', Month, '/', Year, '...');

      Worker.GenerateReport(PrevYear, PrevMonth, False);
      Worker.GenerateReport(PrevYear, 0, True);  //update yearly report & Email          
    finally
      Worker.Free;
    end;
  except
    on E: Exception do
    begin
      WriteLn('Error: ', E.Message);
    end;
  end;
end.

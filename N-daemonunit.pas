{
Key Points:

Replace 'YourAppName' with the actual folder name where your FDB files are stored in user LocalAppData
Update database credentials (DB_USERNAME, DB_PASSWORD) to match your Firebird setup
Adjust the SQL queries in ProcessUserDatabase to match your actual database schema
Modify SCAN_INTERVAL to set how often the daemon scans for databases
The daemon will automatically find and process all user databases without needing user logon

This integrates seamlessly with your existing Lazarus daemon structure and will run continuously, scanning all user LocalAppData directories for FDB files and processing them as the SYSTEM user.

D:\tools>sfk env LOCALAPP
LOCALAPPDATA=C:\Users\<USER>\AppData\Local

}
unit N_DaemonUnit;

{$mode objfpc}{$H+}

interface

uses
  <PERSON><PERSON>, <PERSON>ysUtils, DaemonApp, SysLog, 
  {$IFDEF WINDOWS}Windows,{$ENDIF}
  IBDatabase, IBQuery, IBTransaction;

type
  TDaemon1 = class(TDaemon)
    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
    procedure DataModuleStart(Sender: TCustomDaemon; var OK: Boolean);
    procedure DataModuleStop(Sender: TCustomDaemon; var OK: Boolean);
  private
    FWorkerThread: TThread;
    FStopRequested: Boolean;
    
    // Add these methods
    function GetAllUserLocalAppDataPaths: TStringList;
    function FindFDBFiles(const BasePath: string): TStringList;
    procedure ProcessUserDatabase(const DatabasePath: string);
    procedure ProcessAllUserDatabases;
  public

  end;
// Add to the private section of TDaemon1
const
  APP_NAME = 'snpReports';  // Change this to your actual app folder name
  DB_USERNAME = 'SYSDBA';
  DB_PASSWORD = 'masterkey'; // Change to your actual password
  SCAN_INTERVAL = 300000;    // 5 minutes in milliseconds

var
  Daemon1: TDaemon1;

implementation

{$R *.lfm}

uses uWorker;

function TDaemon1.GetConfigValue(const ValueName, DefaultValue: string): string;
var
  Reg: TRegistry;
begin
  Result := DefaultValue;
  {$IFDEF WINDOWS}
  Reg := TRegistry.Create(KEY_READ);
  try
    Reg.RootKey := HKEY_LOCAL_MACHINE;
    if Reg.OpenKey('SOFTWARE\SNPDaemon', False) then
    begin
      if Reg.ValueExists(ValueName) then
        Result := Reg.ReadString(ValueName);
      Reg.CloseKey;
    end;
  finally
    Reg.Free;
  end;
  {$ENDIF}
end;

// Add these implementations
function TDaemon1.GetAllUserLocalAppDataPaths: TStringList;
var
  {$IFDEF WINDOWS}
  UsersDir: string;
  SearchRec: TSearchRec;
  UserPath: string;
  {$ENDIF}
begin
  Result := TStringList.Create;
  
  {$IFDEF WINDOWS}
  UsersDir := GetEnvironmentVariable('SYSTEMDRIVE') + '\Users\';
  
  if FindFirst(UsersDir + '*', faDirectory, SearchRec) = 0 then
  begin
    repeat
      if (SearchRec.Attr and faDirectory <> 0) and 
         (SearchRec.Name <> '.') and 
         (SearchRec.Name <> '..') and
         (SearchRec.Name <> 'Public') and
         (SearchRec.Name <> 'Default') and
         (SearchRec.Name <> 'All Users') then
      begin
        UserPath := UsersDir + SearchRec.Name + '\AppData\Local\';
        if DirectoryExists(UserPath) then
        begin
          Result.Add(UserPath);
          SysLog.Info('Found user path: ' + UserPath);
        end;
      end;
    until FindNext(SearchRec) <> 0;
    FindClose(SearchRec);
  end;
  {$ENDIF}
  
  {$IFDEF UNIX}
  // For Linux/Unix systems
  Result.Add('/home/');
  {$ENDIF}
end;

function TDaemon1.FindFDBFiles(const BasePath: string): TStringList;
var
  SearchRec: TSearchRec;
  SearchPath: string;
  AppDataPath: string;
begin
  Result := TStringList.Create;
  
  // Adjust this path to match where your app stores FDB files
  AppDataPath := IncludeTrailingPathDelimiter(BasePath) + 'SNP\data\';
  
  if DirectoryExists(AppDataPath) then
  begin
    SearchPath := AppDataPath + '*.fdb';
    
    if FindFirst(SearchPath, faAnyFile and not faDirectory, SearchRec) = 0 then
    begin
      repeat
        Result.Add(AppDataPath + SearchRec.Name);
        SysLog.Info('Found FDB file: ' + AppDataPath + SearchRec.Name);
      until FindNext(SearchRec) <> 0;
      FindClose(SearchRec);
    end;
  end;
end;

procedure TDaemon1.ProcessUserDatabase(const DatabasePath: string);
var
  Database: TIBDatabase;
  Transaction: TIBTransaction;
  Query: TIBQuery;
begin
  if not FileExists(DatabasePath) then
  begin
    SysLog.Warning('Database file not found: ' + DatabasePath);
    Exit;
  end;

  Database := TIBDatabase.Create(nil);
  Transaction := TIBTransaction.Create(nil);
  Query := TIBQuery.Create(nil);
  
  try
    try
      Database.DatabaseName := DatabasePath;
      Database.Params.Clear;
      Database.Params.Add('user_name=SYSDBA');
      Database.Params.Add('password=masterkey'); // Use your actual password
      Database.Params.Add('charset=UTF8');
      
      Transaction.DefaultDatabase := Database;
      Query.Database := Database;
      Query.Transaction := Transaction;
      
      Database.Connected := True;
      Transaction.Active := True;
      
      // Your database processing logic here
      Query.SQL.Text := 'SELECT COUNT(*) as RECORD_COUNT FROM SESSIONS';
      Query.Open;
      
      SysLog.Info('Processing database: ' + DatabasePath + 
                  ', Records: ' + Query.FieldByName('RECORD_COUNT').AsString);
      
      Query.Close;
      Transaction.Commit;
      
    except
      on E: Exception do
      begin
        SysLog.Error('Error processing database ' + DatabasePath + ': ' + E.Message);
        if Transaction.Active then
          Transaction.Rollback;
      end;
    end;
    
  finally
    Database.Connected := False;
    Query.Free;
    Transaction.Free;
    Database.Free;
  end;
end;

procedure TDaemon1.ProcessAllUserDatabases;
var
  UserPaths: TStringList;
  FDBFiles: TStringList;
  i, j: Integer;
begin
  if FStopRequested then Exit;
  
  SysLog.Info('Starting database scan...');
  
  UserPaths := GetAllUserLocalAppDataPaths;
  try
    SysLog.Info('Found ' + IntToStr(UserPaths.Count) + ' user paths');
    
    for i := 0 to UserPaths.Count - 1 do
    begin
      if FStopRequested then Break;
      
      FDBFiles := FindFDBFiles(UserPaths[i]);
      try
        for j := 0 to FDBFiles.Count - 1 do
        begin
          if FStopRequested then Break;
          
          ProcessUserDatabase(FDBFiles[j]);
        end;
      finally
        FDBFiles.Free;
      end;
    end;
  finally
    UserPaths.Free;
  end;
  
  SysLog.Info('Database scan completed');
end;

procedure TDaemon1.DataModuleCreate(Sender: TObject);
begin
  FStopRequested := False;
  SysLog.Info('SNP Daemon created');
end;

procedure TDaemon1.DataModuleDestroy(Sender: TObject);
begin
  FStopRequested := True;
  SysLog.Info('SNP Daemon destroyed');
end;

procedure TDaemon1.DataModuleStart(Sender: TCustomDaemon; var OK: Boolean);
begin
  SysLog.Info('SNP Daemon starting...');
  
  // Create your worker thread that will call ProcessAllUserDatabases
  FWorkerThread := TThread.CreateAnonymousThread(
    procedure
    begin
      while not FStopRequested do
      begin
        try
          ProcessAllUserDatabases;
        except
          on E: Exception do
            SysLog.Error('Worker thread error: ' + E.Message);
        end;
        
        // Sleep for 5 minutes (adjust as needed)
        Sleep(300000);
      end;
    end
  );
  
  FWorkerThread.Start;
  OK := True;
  SysLog.Info('SNP Daemon started successfully');
end;

procedure TDaemon1.DataModuleStop(Sender: TCustomDaemon; var OK: Boolean);
begin
  SysLog.Info('SNP Daemon stopping...');
  
  FStopRequested := True;
  
  if Assigned(FWorkerThread) then
  begin
    FWorkerThread.Terminate;
    FWorkerThread.WaitFor;
    FWorkerThread.Free;
    FWorkerThread := nil;
  end;
  
  OK := True;
  SysLog.Info('SNP Daemon stopped');
end;

end.
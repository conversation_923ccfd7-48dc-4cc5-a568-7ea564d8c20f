<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <PathDelim Value="\"/>
    <Version Value="12"/>
    <BuildModes Active="Release"/>
    <Units>
      <Unit>
        <Filename Value="snpreport.lpr"/>
        <IsPartOfProject Value="True"/>
        <TopLine Value="7"/>
        <UsageCount Value="20"/>
        <Loaded Value="True"/>
        <DefaultSyntaxHighlighter Value="Delphi"/>
      </Unit>
      <Unit>
        <Filename Value="uMain.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="20"/>
        <DefaultSyntaxHighlighter Value="Delphi"/>
      </Unit>
      <Unit>
        <Filename Value="uDB.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="20"/>
        <DefaultSyntaxHighlighter Value="Delphi"/>
      </Unit>
      <Unit>
        <Filename Value="uReports.pas"/>
        <IsPartOfProject Value="True"/>
        <IsVisibleTab Value="True"/>
        <EditorIndex Value="1"/>
        <TopLine Value="28"/>
        <CursorPos X="2" Y="37"/>
        <UsageCount Value="20"/>
        <Loaded Value="True"/>
        <DefaultSyntaxHighlighter Value="Delphi"/>
      </Unit>
      <Unit>
        <Filename Value="uUtils.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="20"/>
        <DefaultSyntaxHighlighter Value="Delphi"/>
      </Unit>
    </Units>
    <JumpHistory HistoryIndex="-1"/>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
</CONFIG>

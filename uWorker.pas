unit uWorker;

{$mode ObjFPC}{$H+}

interface

uses
  Classes, SysUtils, DateUtils, uDB, uReports, uUtils,
  zipper, IniFiles, smtpsend, ssl_openssl3, mimemess, mimepart;

type
  TReportWorker = class(TThread)
  private
    FDatabase: TSnpDatabase;
    FReport: TSnpReport;
    FOutputDir: string;
    FDebugMode: Boolean;
    FEmailRecipient: String;
    
    function ZipReports(const SourceDir: string): string;
    procedure LoadSMTPConfig(out SMTPHost: string; out SMTPUser: string; out SMTPPass: string; 
      out SMTPFrom: string; out SMTPPort: Integer; out UseSSL: Boolean);
    function SendEmailWithAttachment(const ZipFilePath: string): Boolean;
    procedure LogToFile(const Msg: string);
    function GetOutputDirectory: string;
    function TestDirectoryWritable(const DirPath: string): Boolean;
  protected
    procedure Execute; override;
  public
    constructor Create(DebugMode: Boolean = False);
    destructor Destroy; override;
    procedure GenerateReport(const Year: Integer; const Month: Integer; SendEmail: Boolean = False);
  end;

implementation

uses
  FileUtil, Windows, ShlObj, Registry;

constructor TReportWorker.Create(DebugMode: Boolean = False);
begin
  FDebugMode := DebugMode;

  FEmailRecipient := '<EMAIL>';

  if FDebugMode then
    LogToFile('TReportWorker.Create - Entering');
    
  inherited Create(True); // Create suspended
  
  if FDebugMode then
    LogToFile('TReportWorker.Create - After inherited Create');
    
  FreeOnTerminate := False;
  
  if FDebugMode then
    LogToFile('TReportWorker.Create - Creating database');
  FDatabase := TSnpDatabase.Create;
  
  if FDebugMode then
    LogToFile('TReportWorker.Create - Creating report');
  FReport := TSnpReport.Create(FDatabase);
  
  if FDebugMode then
    LogToFile('TReportWorker.Create - Worker created successfully');
end;

destructor TReportWorker.Destroy;
begin
  LogToFile('TReportWorker.Destroy - Entering');
  try
    if Assigned(FReport) then
    begin
      FReport.Free;
      FReport := nil;
    end;
    
    if Assigned(FDatabase) then
    begin
      FDatabase.Free;
      FDatabase := nil;
    end;
    
    LogToFile('TReportWorker.Destroy - Resources freed');
  except
    on E: Exception do
      LogToFile('Error in TReportWorker.Destroy: ' + E.Message);
  end;
  
  inherited Destroy;
  LogToFile('TReportWorker.Destroy - Completed');
end;

procedure TReportWorker.LogToFile(const Msg: string);
var
  LogFile: TextFile;
  LogPath: string;
begin
  if not FDebugMode then Exit;
  // Forward the message to DaemonApp's logging system
  //Exit;
  try
    LogPath := ChangeFileExt(ParamStr(0), '.log');
    AssignFile(LogFile, LogPath);
    
    if FileExists(LogPath) then
      Append(LogFile)
    else
      Rewrite(LogFile);
      
    try
      WriteLn(LogFile, FormatDateTime('yyyy-mm-dd hh:nn:ss.zzz', Now) + ' - ' + Msg);
    finally
      CloseFile(LogFile);
    end;
  except
    // Silently fail in production
    on E: Exception do
      if FDebugMode then
        WriteLn('Error writing to log: ', E.Message);
  end;
end;

function TReportWorker.TestDirectoryWritable(const DirPath: string): Boolean;
var
  TestFile: TextFile;
  TestFilePath: string;
begin
  Result := False;
  if not DirectoryExists(DirPath) then
    Exit;

  TestFilePath := IncludeTrailingPathDelimiter(DirPath) + 'write_test_' + IntToStr(GetTickCount64) + '.tmp';

  try
    AssignFile(TestFile, TestFilePath);
    try
      Rewrite(TestFile);
      WriteLn(TestFile, 'Write test');
      CloseFile(TestFile);

      // If we got here, write was successful
      Result := True;

      // Clean up test file
      if FileExists(TestFilePath) then
        DeleteFile(PChar(TestFilePath));

    except
      // File operations failed - directory not writable
      Result := False;
      try
        CloseFile(TestFile);
      except
        // Ignore close errors
      end;
    end;
  except
    // Any other error means not writable
    Result := False;
  end;
end;

function TReportWorker.GetOutputDirectory: string;
var
  Path: array[0..MAX_PATH] of Char;
  ProgramDataPath: string;
begin
  try
    // Use ProgramData\SNP\Reports directory for reports (writable by daemon services)
    // This ensures reports are stored in the same location as the Firebird3 database
    // which is in ProgramData\SNP\data\
    if SHGetFolderPath(0, CSIDL_COMMON_APPDATA, 0, 0, @Path) = S_OK then
    begin
      ProgramDataPath := IncludeTrailingPathDelimiter(Path) + 'SNP\Reports';
    end
    else
    begin
      // Fallback to C:\ProgramData if SHGetFolderPath fails
      ProgramDataPath := 'C:\ProgramData\SNP\Reports';
    end;

    // Ensure the directory exists
    if not DirectoryExists(ProgramDataPath) then
    begin
      try
        ForceDirectories(ProgramDataPath);
        LogToFile('Created report directory: ' + ProgramDataPath);
      except
        on E: Exception do
        begin
          LogToFile('Error creating report directory: ' + E.Message);
          ProgramDataPath := ''; // Mark as failed
        end;
      end;
    end;

    // Test if directory is writable
    if (ProgramDataPath <> '') and TestDirectoryWritable(ProgramDataPath) then
    begin
      Result := ProgramDataPath;
      LogToFile('Using ProgramData report directory: ' + Result);
    end
    else
    begin
      // Try fallback location
      Result := 'C:\SNP\Reports';
      LogToFile('ProgramData directory not writable, trying fallback: ' + Result);

      if not DirectoryExists(Result) then
        ForceDirectories(Result);

      if not TestDirectoryWritable(Result) then
      begin
        LogToFile('Warning: Fallback directory may not be writable: ' + Result);
      end;
    end;

  except
    on E: Exception do
    begin
      // Final fallback to a default location if there's an error
      Result := 'C:\SNP\Reports';
      if not DirectoryExists(Result) then
        ForceDirectories(Result);
      LogToFile('Error getting ProgramData path, using default directory: ' + E.Message);
    end;
  end;
end;

procedure TReportWorker.GenerateReport(const Year: Integer; const Month: Integer; SendEmail: Boolean = False);
var
  OutputDir: String;
  StartTime: TDateTime;
  ZipFilePath: String;
begin
  StartTime := Now;
  LogToFile('Starting report generation at ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', StartTime));
  
  // Get the proper output directory using database path
  OutputDir := GetOutputDirectory;
  
  LogToFile('Using output directory: ' + OutputDir);
  ForceDirectories(OutputDir);
  
  try
    LogToFile('Connecting to database...');
    FDatabase.Connect;
    LogToFile('Successfully connected to database');
    
    if Month > 0 then
    begin
      LogToFile('Generating monthly report for ' + IntToStr(Year) + '-' + Format('%.2d', [Month]) + '...');
      FReport.GenerateMonthlyReport(Year, Month, OutputDir);
      LogToFile('Monthly report generated successfully');
    end
    else
    begin
      LogToFile('Generating yearly report for ' + IntToStr(Year) + '...');
      FReport.GenerateYearlyReport(Year, OutputDir);
      LogToFile('Yearly report generated successfully');
    end;
    
    if SendEmail then
    begin
      // Zip the reports for email attachment
      LogToFile('Creating zip archive for email...');
      ZipFilePath := ZipReports(OutputDir);

      if ZipFilePath <> '' then
      begin
        // Send email with zip attachment
        LogToFile('Sending email with reports...');
        if SendEmailWithAttachment(ZipFilePath) then
        begin
          // Clean up ZIP file after successful email sending
          try
            if FileExists(ZipFilePath) then
            begin
              DeleteFile(PChar(ZipFilePath));
              WriteLn('ZIP file cleaned up after successful email sending');
            end;
          except
            on E: Exception do
              WriteLn('Warning: Could not delete ZIP file: ', E.Message);
          end;
        end
        else
        begin
          WriteLn('Email sending failed - ZIP file preserved at: ', ZipFilePath);
        end;
      end
      else
      begin
        WriteLn('Failed to create zip file - email not sent');
      end;
    end;

    //WriteLn('Report files saved to: ', ExpandFileName(OutputDir));
  except
    on E: Exception do
    begin
      LogToFile('ERROR: ' + E.ClassName + ': ' + E.Message);
      LogToFile('Report generation failed after ' + FloatToStr(MilliSecondsBetween(Now, StartTime) / 1000) + ' seconds');
      Halt(1);
    end;
  end;
  
end;

procedure TReportWorker.Execute;
var
  CurrentDate: TDateTime;
  PrevMonth, PrevYear, PrevDay: Word;
  LastRunDate: TDate;
begin
  LogToFile('Report worker started');
  LastRunDate := 0;
  try
    // Initialize output directory
    GetOutputDirectory;
    
    // Main worker loop
    while not Terminated do
    begin
      try
        CurrentDate := Now;
        LogToFile('Worker tick - Current date: ' + DateToStr(CurrentDate) + 
                 ', Day: ' + IntToStr(DayOf(CurrentDate)) + 
                 ', Last run: ' + DateToStr(LastRunDate));
        
        // Check if it's the 2nd day of the month
        if (DayOf(CurrentDate) = 28) then
        begin
          LogToFile('2nd day of month detected - checking if we should run...');
          // Get previous month
          DecodeDate(IncMonth(CurrentDate, -1), PrevYear, PrevMonth, PrevDay);
          
          // Only run once per day on the 2nd
          if (Trunc(CurrentDate) > LastRunDate) then
          begin
            LogToFile('Last run was on ' + DateToStr(LastRunDate) + ' - proceeding with report generation');
            // Generate report for previous month
            GenerateReport(PrevYear, PrevMonth, False);
            GenerateReport(PrevYear, 0, True);  //update yearly report & Email
            // Update last run date to today
            LastRunDate := Trunc(CurrentDate);
            
            LogToFile('Reports generated successfully for ' + IntToStr(PrevYear) + '-' + Format('%.2d', [PrevMonth]));
            // Sleep for 25 hours to ensure we don't run again today
            //Sleep(25 * 60 * 60 * 1000);
          end;
        end;
        
        // Sleep for 1 hour before checking again
        Sleep(60 * 1000);
        
      except
        on E: Exception do
        begin
          LogToFile('Error in worker thread: ' + E.Message);
          // Wait 5 minutes before retrying on error
          Sleep(5 * 60 * 1000);
        end;
      end;
    end;
    
  finally
    LogToFile('Report worker stopped');
  end;
end;

function TReportWorker.ZipReports(const SourceDir: string): string;
var
  Zipper: TZipper;
  SearchRec: TSearchRec;
  ZipFileName: string;
  FileList: TStringList;
  i: Integer;
  LogMsg: string;
begin
  Result := '';
  ZipFileName := IncludeTrailingPathDelimiter(ExtractFilePath(SourceDir)) +
                 'SNP_Reports_' + FormatDateTime('yyyy-mm-dd_hh-nn-ss', Now) + '.zip';

  FileList := TStringList.Create;
  Zipper := TZipper.Create;
  try
    // Find all HTML files in the reports directory
    try
      if SysUtils.FindFirst(IncludeTrailingPathDelimiter(SourceDir) + '*.html', faAnyFile, SearchRec) = 0 then
      begin
        repeat
          if (SearchRec.Attr and faDirectory) = 0 then  // Not a directory
            FileList.Add(IncludeTrailingPathDelimiter(SourceDir) + SearchRec.Name);
        until SysUtils.FindNext(SearchRec) <> 0;
        SysUtils.FindClose(SearchRec);
      end;
    except
      on E: Exception do
      begin
        LogMsg := 'Error searching for HTML files: ' + E.Message;
        if FDebugMode then WriteLn(LogMsg);
        LogToFile(LogMsg);
      end;
    end;

    // Add reports.json if it exists
    try
      if FileExists(IncludeTrailingPathDelimiter(SourceDir) + 'reports.json') then
      begin
        FileList.Add(IncludeTrailingPathDelimiter(SourceDir) + 'reports.json');
        LogMsg := 'Added reports.json to zip list';
        if FDebugMode then WriteLn(LogMsg);
        LogToFile(LogMsg);
      end
      else
      begin
        LogMsg := 'reports.json not found in: ' + IncludeTrailingPathDelimiter(SourceDir);
        if FDebugMode then WriteLn(LogMsg);
        LogToFile(LogMsg);
      end;
    except
      on E: Exception do
      begin
        LogMsg := 'Error checking for reports.json: ' + E.Message;
        if FDebugMode then WriteLn(LogMsg);
        LogToFile(LogMsg);
      end;
    end;

    if FileList.Count > 0 then
    begin
      try
        Zipper.FileName := ZipFileName;
        for i := 0 to FileList.Count - 1 do
          Zipper.Entries.AddFileEntry(FileList[i], ExtractFileName(FileList[i]));
        Zipper.ZipAllFiles;
        Result := ZipFileName;
        LogMsg := 'Created zip file: ' + ZipFileName;
        if FDebugMode then WriteLn(LogMsg);
        LogToFile(LogMsg);
      except
        on E: Exception do
        begin
          LogMsg := 'Error creating zip file: ' + E.Message;
          if FDebugMode then WriteLn(LogMsg);
          LogToFile(LogMsg);
        end;
      end;
    end
    else
    begin
      LogMsg := 'No files found to zip in ' + SourceDir;
      if FDebugMode then WriteLn(LogMsg);
      LogToFile(LogMsg);
    end;
  finally
    Zipper.Free;
    FileList.Free;
  end;
end;

procedure TReportWorker.LoadSMTPConfig(out SMTPHost, SMTPUser, SMTPPass, SMTPFrom: String; out SMTPPort: Integer; out UseSSL: Boolean);
var
  ConfigFile: TIniFile;
  ConfigPath, SSLStr: String;
begin
  // Default values
  SMTPHost := 'smtp.gmail.com';
  SMTPPort := 587;
  UseSSL := True;
  SMTPUser := '<EMAIL>';
  SMTPPass := 'hquaqkebkitjyvlt';
  SMTPFrom := '<EMAIL>';

  // Try to load from config.ini
  ConfigPath := ExtractFilePath(ParamStr(0)) + 'config.ini';

  if FileExists(ConfigPath) then
  begin
    ConfigFile := TIniFile.Create(ConfigPath);
    try
      SMTPHost := ConfigFile.ReadString('SMTP', 'Host', SMTPHost);
      SMTPPort := ConfigFile.Readinteger('SMTP', 'Port', SMTPPort);
      // Read UseSSL more robustly to handle lowercase 'true'
      SSLStr := LowerCase(Trim(ConfigFile.ReadString('SMTP', 'UseSSL', 'true')));
      UseSSL := (SSLStr = 'true') or (SSLStr = '1') or (SSLStr = 'yes');
      SMTPUser := ConfigFile.ReadString('SMTP', 'Username', SMTPUser);
      SMTPPass := ConfigFile.ReadString('SMTP', 'Password', SMTPPass);
      SMTPFrom := ConfigFile.ReadString('SMTP', 'FromEmail', SMTPFrom);

      // If FromEmail is not set, use Username as default
      if SMTPFrom = '' then
        SMTPFrom := SMTPUser;

    finally
      ConfigFile.Free;
    end;
  end;
  // else
  // begin
  //   // Create a sample config file with instructions
  //   ConfigFile := TIniFile.Create(ConfigPath);
  //   try
  //     ConfigFile.WriteString('SMTP', 'Host', SMTPHost);
  //     ConfigFile.WriteInteger('SMTP', 'Port', SMTPPort);
  //     ConfigFile.WriteBool('SMTP', 'UseSSL', UseSSL);
  //     ConfigFile.WriteString('SMTP', 'Username', '');
  //     ConfigFile.WriteString('SMTP', 'Password', '');
  //     ConfigFile.WriteString('SMTP', 'FromEmail', '');

  //     // Add comments section
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Host', 'SMTP server (e.g., smtp.gmail.com, smtp.outlook.com)');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Port', '587 for TLS, 465 for SSL, 25 for plain');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'UseSSL', 'true for secure connection, false for plain');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Username', 'Your email address');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Password', 'Your email password or app password');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'FromEmail', 'Email address to send from (usually same as Username)');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Gmail_Note', 'For Gmail use App Password, not regular password');
  //     ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Outlook_Note', 'For Outlook use smtp.outlook.com port 587');

  //   finally
  //     ConfigFile.Free;
  //   end;

  //  WriteLn('Created sample config file: ', ConfigPath);
  //  WriteLn('Please edit the [SMTP] section with your email credentials.');
  //end;
end;

function TReportWorker.SendEmailWithAttachment(const ZipFilePath: String): Boolean;
var
  SMTP: TSMTPSend;
  Msg: TMimeMess;
  Part: TMimePart;
  Subject, Body: String;
  BodyLines: TStringList;
  SMTPHost, SMTPUser, SMTPPass, SMTPFrom: String;
  SMTPPort: Integer;
  UseSSL: Boolean;
begin
  Result := False;

  if not FileExists(ZipFilePath) then
  begin
    LogToFile('Zip file not found: ' + ZipFilePath);
    Exit;
  end;

  LogToFile('Loading SMTP configuration...');
  LoadSMTPConfig(SMTPHost, SMTPUser, SMTPPass, SMTPFrom, SMTPPort, UseSSL);

  // Check if SMTP is configured
  if (SMTPUser = '') or (SMTPPass = '') then
  begin
    LogToFile('SMTP credentials not configured in config.ini');
    LogToFile('Please edit config.ini and add your SMTP settings in the [SMTP] section.');
    LogToFile('ZIP file is available at: ' + ZipFilePath);
    Result := False;
    Exit;
  end;

  LogToFile('Sending email via Synapse SMTP...');
  LogToFile('SMTP Server: ' + SMTPHost + ':' + IntToStr(SMTPPort));
  LogToFile('From: ' + SMTPFrom);
  LogToFile('To: ' + FEmailRecipient);
  LogToFile('SSL/TLS: ' + BoolToStr(UseSSL, True));

  try
    Subject := 'SNP Reports - ' + FormatDateTime('yyyy-mm-dd', Now);
    Body := 'Dear Recipient,' + #13#10#13#10 +
            'Please find attached the latest SNP reports generated on ' +
            FormatDateTime('yyyy-mm-dd hh:nn:ss', Now) + '.' + #13#10#13#10 +
            'The attached ZIP file contains:' + #13#10 +
            '- Monthly and/or yearly reports in HTML format' + #13#10 +
            '- Generated automatically by SNP Report Generator' + #13#10#13#10 +
            'Best regards,' + #13#10 +
            'SNP Report System';

    SMTP := TSMTPSend.Create;
    Msg := TMimeMess.Create;
    BodyLines := TStringList.Create;
    try
      // Configure SMTP
      SMTP.TargetHost := SMTPHost;
      SMTP.TargetPort := IntToStr(SMTPPort);
      SMTP.Username := SMTPUser;
      SMTP.Password := SMTPPass;
      SMTP.Timeout := 60000; // 60 seconds timeout

      if UseSSL then
      begin
        if SMTPPort = 465 then
        begin
          SMTP.FullSSL := True;  // SSL
          SMTP.AutoTLS := False;
        end
        else
        begin
          SMTP.AutoTLS := True;  // TLS (STARTTLS)
          SMTP.FullSSL := False;
        end;
      end
      else
      begin
        SMTP.AutoTLS := False;
        SMTP.FullSSL := False;
      end;

      // Create message
      Msg.Header.From := SMTPFrom;
      Msg.Header.ToList.Add(FEmailRecipient);
      Msg.Header.Subject := Subject;
      Msg.Header.Date := Now;
      Msg.Header.MessageID := '<' + FormatDateTime('yyyymmddhhnnss', Now) + '@snpreport>';

      // Add body as multipart
      Part := Msg.AddPartMultipart('mixed', nil);
      BodyLines.Text := Body;
      Msg.AddPartText(BodyLines, Part);

      // Add ZIP attachment
      LogToFile('Adding attachment: ' + ExtractFileName(ZipFilePath));
      Msg.AddPartBinaryFromFile(ZipFilePath, Part);

      // Debug: Show message details
      LogToFile('Debug: Message From: ' + Msg.Header.From);
      LogToFile('Debug: Message To: ' + Msg.Header.ToList.Text);
      LogToFile('Debug: Message Subject: ' + Msg.Header.Subject);
      LogToFile('Debug: Message parts count: ' + IntToStr(Msg.MessagePart.GetSubPartCount));

      // Encode the message to generate final email content
      Msg.EncodeMessage;

      // Send email
      LogToFile('Connecting to SMTP server...');
      LogToFile('Debug: Host=' + SMTP.TargetHost + ', Port=' + SMTP.TargetPort);
      LogToFile('Debug: Username=' + SMTP.Username);
      LogToFile('Debug: AutoTLS=' + BoolToStr(SMTP.AutoTLS, True) + ', FullSSL=' + BoolToStr(SMTP.FullSSL, True));

      if SMTP.Login then
      begin
        LogToFile('Connected successfully. Sending email...');

        LogToFile('Debug: Email content preview (first 500 chars):');
        LogToFile(Copy(Msg.Lines.Text, 1, 500));
        LogToFile('Debug: Total email size: ' + IntToStr(Length(Msg.Lines.Text)) + ' characters');

        if SMTP.MailFrom(SMTPFrom, Length(Msg.Lines.Text)) then
        begin
          if SMTP.MailTo(FEmailRecipient) then
          begin
            if SMTP.MailData(Msg.Lines) then
            begin
              LogToFile('Email sent successfully to: ' + FEmailRecipient);
              LogToFile('Subject: ' + Subject);
              LogToFile('Attachment: ' + ExtractFileName(ZipFilePath));
              Result := True;
            end
            else
            begin
              LogToFile('Failed to send email data');
              LogToFile('SMTP Error: ' + SMTP.ResultString);
            end;
          end
          else
          begin
            LogToFile('Failed to set recipient: ' + FEmailRecipient);
            LogToFile('SMTP Error: ' + SMTP.ResultString);
            WriteLn('SMTP Error: ', SMTP.ResultString);
          end;
        end
        else
        begin
          WriteLn('Failed to set sender: ', SMTPFrom);
          WriteLn('SMTP Error: ', SMTP.ResultString);
        end;

        SMTP.Logout;
      end
      else
      begin
        WriteLn('Failed to connect to SMTP server: ', SMTPHost, ':', SMTPPort);
        WriteLn('SMTP Error Code: ', SMTP.ResultCode);
        WriteLn('SMTP Error Message: ', SMTP.ResultString);
        WriteLn('SMTP Full Response: ', SMTP.FullResult.Text);
        WriteLn('');
        WriteLn('Troubleshooting:');
        WriteLn('  - SMTP server address and port');
        WriteLn('  - Username and password (Gmail requires App Password)');
        WriteLn('  - Internet connection');
        WriteLn('  - Firewall settings');
        if UseSSL then
        begin
          WriteLn('  - SSL/TLS support (try port 587 for TLS or 465 for SSL)');
          WriteLn('  - For Gmail: Ensure 2FA is enabled and using App Password');
        end;
      end;

    finally
      BodyLines.Free;
      Msg.Free;
      SMTP.Free;
    end;

  except
    on E: Exception do
    begin
      WriteLn('Error sending email: ', E.Message);
      WriteLn('ZIP file is available at: ', ZipFilePath);
      WriteLn('Please check your SMTP configuration in config.ini');
    end;
  end;
end;

end.

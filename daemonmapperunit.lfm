object SnpDaemonMapper: TsnpDaemonMapper
  DaemonDefs = <  
    item
      DaemonClassName = 'TsnpDaemon'
      Name = 'snpdaemon'
      Description = 'SNP Report Daemon'
      DisplayName = 'SNP Report Daemon'
      Options = [doAllowStop, doAllowPause]
      WinBindings.Dependencies = <>
      WinBindings.StartType = stAuto
      WinBindings.WaitHint = 0
      WinBindings.IDTag = 0
      WinBindings.ServiceType = stWin32
      WinBindings.ErrorSeverity = esIgnore
      WinBindings.AcceptedCodes = []
      LogStatusReport = False
    end>
  Left = 371
  Top = 31
end
